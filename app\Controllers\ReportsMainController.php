<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\ExerciseModel;

/**
 * ReportsMainController
 *
 * Main controller for reports system - handles exercise listing, dashboard, and navigation
 */
class ReportsMainController extends Controller
{
    protected $exerciseModel;

    public function __construct()
    {
        helper(['url', 'form']);
        $this->exerciseModel = new ExerciseModel();
    }

    /**
     * [GET] Main reports index - redirects to exercises list
     * URI: /reports
     */
    public function index()
    {
        return redirect()->to('reports/exercises');
    }

    /**
     * [GET] Display exercises for reports (all statuses)
     * URI: /reports/exercises
     */
    public function exercises()
    {
        try {
            // Get exercises with statistics from database (all statuses)
            $orgId = session()->get('org_id');

            // For debugging - log the org_id being used
            log_message('info', 'Reports exercises - Using org_id: ' . ($orgId ?? 'null'));

            // Get all exercises regardless of status
            $exercises = $this->exerciseModel->getExercisesForReports($orgId);

            // For debugging - log the number of exercises found
            log_message('info', 'Reports exercises - Found ' . count($exercises) . ' exercises');

            $data = [
                'title' => 'Reports - Exercises',
                'menu' => 'reports',
                'exercises' => $exercises
            ];

            return view('application_reports/appx_reports_exercises', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercises for reports: ' . $e->getMessage());

            $data = [
                'title' => 'Reports - Exercises',
                'menu' => 'reports',
                'exercises' => []
            ];

            return view('application_reports/appx_reports_exercises', $data);
        }
    }

    /**
     * [GET] Display reports dashboard for specific exercise
     * URI: /reports/dashboard/{exerciseId}
     */
    public function dashboard($exerciseId)
    {
        try {
            // Get exercise with statistics from database
            $exercise = $this->exerciseModel->getExerciseWithStatistics($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            $statistics = $exercise['statistics'] ?? [];

            $data = [
                'title' => 'Reports Dashboard - ' . $exercise['exercise_name'],
                'menu' => 'reports',
                'exercise' => $exercise,
                'statistics' => $statistics
            ];

            return view('application_reports/appx_reports_dashboard', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercise dashboard data: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading exercise dashboard');
        }
    }

    /**
     * [GET] Display positions for exercise reports
     * URI: /reports/positions/{exerciseId}
     */
    public function positions($exerciseId)
    {
        try {
            // Get exercise data
            $exercise = $this->exerciseModel->find($exerciseId);
            if (!$exercise) {
                return redirect()->to('reports/exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get positions with statistics
            $positions = $this->exerciseModel->getPositionsWithStatistics($exerciseId);

            $data = [
                'title' => 'Reports - Positions',
                'menu' => 'reports',
                'exercise' => $exercise,
                'positions' => $positions
            ];

            return view('application_reports/appx_reports_positions', $data);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching positions for reports: ' . $e->getMessage());
            return redirect()->to('reports/exercises')
                            ->with('error', 'Error loading positions data');
        }
    }

}
