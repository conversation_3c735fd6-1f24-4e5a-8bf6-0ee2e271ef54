<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table         = 'exercises';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_name',
        'gazzetted_no',
        'gazzetted_date',
        'advertisement_no',
        'advertisement_date',
        'is_internal',
        'mode_of_advertisement',
        'publish_date_from',
        'publish_date_to',
        'description',
        'pre_screen_criteria',
        'applicants_information',
        'applicants_notice',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    // Simple validation rules - allow updates with partial data
    protected $validationRules = [
        'org_id'             => 'permit_empty|numeric',
        'exercise_name'      => 'permit_empty|max_length[255]',
        'gazzetted_no'       => 'permit_empty|max_length[255]',
        'gazzetted_date'     => 'permit_empty|valid_date',
        'advertisement_no'   => 'permit_empty|max_length[255]',
        'advertisement_date' => 'permit_empty|valid_date',
        'is_internal'        => 'permit_empty|in_list[0,1]',
        'mode_of_advertisement' => 'permit_empty|max_length[100]',
        'publish_date_from'  => 'permit_empty|valid_date',
        'publish_date_to'    => 'permit_empty|valid_date',
        'status'             => 'permit_empty|in_list[published,draft,selection,archived]',
        'created_by'         => 'permit_empty|numeric',
        'updated_by'         => 'permit_empty|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_name' => [
            'required'   => 'Exercise name is required',
            'max_length' => 'Exercise name cannot exceed 255 characters'
        ],
        'is_internal' => [
            'in_list' => 'Advertisement type must be either internal (1) or external (0)'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getExercisesByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }

    /**
     * Get active exercises (published status)
     *
     * @return array
     */
    public function getActiveExercises()
    {
        return $this->where('status', 'publish')
                    ->where('publish_date_from <=', date('Y-m-d'))
                    ->where('publish_date_to >=', date('Y-m-d'))
                    ->findAll();
    }

    /**
     * Get draft exercises
     *
     * @param int $orgId
     * @return array
     */
    public function getDraftExercises($orgId = null)
    {
        $builder = $this->where('status', 'draft');

        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }

        return $builder->findAll();
    }

    /**
     * Get exercises available for pre-screening (status != 'draft')
     *
     * @param int $orgId Optional organization ID to filter by
     * @return array
     */
    public function getPreScreeningExercises($orgId = null)
    {
        $builder = $this->where('status !=', 'draft')
                        ->orderBy('created_at', 'DESC');

        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }

        return $builder->findAll();
    }

    /**
     * Get exercises with statistics for reports (published and selection status)
     *
     * @param int $orgId Optional organization ID to filter by
     * @return array
     */
    public function getExercisesForReports($orgId = null)
    {
        $builder = $this->db->table('exercises e')
            ->select('e.*,
                     dakoii_org.org_name,
                     COUNT(DISTINCT p.id) as position_count,
                     COUNT(DISTINCT a.id) as application_count,
                     COUNT(DISTINCT CASE WHEN a.status = "shortlisted" THEN a.id END) as shortlisted_count')
            ->join('dakoii_org', 'e.org_id = dakoii_org.id', 'left')
            ->join('positions_groups pg', 'e.id = pg.exercise_id', 'left')
            ->join('positions p', 'pg.id = p.position_group_id', 'left')
            ->join('applications a', 'p.id = a.position_id', 'left')
            ->whereIn('e.status', ['published', 'selection'])
            ->where('e.deleted_at IS NULL')
            ->groupBy('e.id')
            ->orderBy('e.created_at', 'DESC');

        if ($orgId !== null) {
            $builder->where('e.org_id', $orgId);
        }

        return $builder->get()->getResultArray();
    }

    /**
     * Get exercise with detailed statistics for dashboard
     *
     * @param int $exerciseId
     * @return array|null
     */
    public function getExerciseWithStatistics($exerciseId)
    {
        $exercise = $this->find($exerciseId);
        if (!$exercise) {
            return null;
        }

        // Get statistics
        $builder = $this->db->table('exercises e')
            ->select('COUNT(DISTINCT a.id) as total_applications,
                     COUNT(DISTINCT CASE WHEN a.pre_screening_status = "passed" THEN a.id END) as pre_screened_passed,
                     COUNT(DISTINCT CASE WHEN a.pre_screening_status = "failed" THEN a.id END) as pre_screened_failed,
                     COUNT(DISTINCT CASE WHEN a.status = "shortlisted" THEN a.id END) as shortlisted,
                     COUNT(DISTINCT CASE WHEN a.status = "interviewed" THEN a.id END) as interviewed,
                     COUNT(DISTINCT CASE WHEN a.status = "selected" THEN a.id END) as selected,
                     COUNT(DISTINCT p.id) as total_positions')
            ->join('positions_groups pg', 'e.id = pg.exercise_id', 'left')
            ->join('positions p', 'pg.id = p.position_group_id', 'left')
            ->join('applications a', 'p.id = a.position_id', 'left')
            ->where('e.id', $exerciseId)
            ->where('e.deleted_at IS NULL');

        $statistics = $builder->get()->getRowArray();

        // Calculate additional statistics
        $statistics['positions_filled'] = $statistics['selected'] ?? 0;
        $statistics['positions_vacant'] = max(0, ($statistics['total_positions'] ?? 0) - ($statistics['selected'] ?? 0));

        $exercise['statistics'] = $statistics;
        return $exercise;
    }

    /**
     * Get positions with statistics for an exercise
     *
     * @param int $exerciseId
     * @return array
     */
    public function getPositionsWithStatistics($exerciseId)
    {
        $builder = $this->db->table('positions p')
            ->select('p.*,
                     pg.group_name,
                     COUNT(DISTINCT a.id) as application_count,
                     COUNT(DISTINCT CASE WHEN a.status = "shortlisted" THEN a.id END) as shortlisted_count,
                     COUNT(DISTINCT CASE WHEN a.status = "interviewed" THEN a.id END) as interviewed_count,
                     COUNT(DISTINCT CASE WHEN a.status = "selected" THEN a.id END) as selected_count')
            ->join('positions_groups pg', 'p.position_group_id = pg.id', 'left')
            ->join('applications a', 'p.id = a.position_id', 'left')
            ->where('pg.exercise_id', $exerciseId)
            ->where('p.deleted_at IS NULL')
            ->groupBy('p.id')
            ->orderBy('pg.group_name, p.designation');

        return $builder->get()->getResultArray();
    }
}