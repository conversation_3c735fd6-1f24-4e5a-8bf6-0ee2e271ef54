<?php
/**
 * View file for listing exercises for reports
 *
 * @var array $exercises List of exercises with selection status
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h3 mb-1">Reports</h2>
                    <p class="text-muted mb-0">Generate and view recruitment reports for exercises</p>
                </div>
                <div>
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Dashboard
                    </a>
                    <span class="badge bg-info fs-6">
                        <i class="fas fa-chart-bar me-1"></i>
                        Reports Dashboard
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercises List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-alt me-2"></i>
                        Exercises Available for Reports
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($exercises)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No exercises available for reports</h5>
                            <p class="text-muted">Only exercises with 'selection' status are available for reporting.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Exercise Details</th>
                                        <th>Advertisement Info</th>
                                        <th>Statistics</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($exercises as $exercise): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-1"><?= esc($exercise['exercise_name']) ?></h6>
                                                    <small class="text-muted">
                                                        <strong>Gazette:</strong> <?= esc($exercise['gazzetted_no']) ?>
                                                        (<?= date('M d, Y', strtotime($exercise['gazzetted_date'])) ?>)
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <small class="text-muted d-block">
                                                        <strong>Ad No:</strong> <?= esc($exercise['advertisement_no']) ?>
                                                    </small>
                                                    <small class="text-muted d-block">
                                                        <strong>Published:</strong> <?= date('M d, Y', strtotime($exercise['publish_date_from'])) ?> -
                                                        <?= date('M d, Y', strtotime($exercise['publish_date_to'])) ?>
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-column">
                                                    <small class="text-muted">
                                                        <i class="fas fa-briefcase me-1"></i>
                                                        <?= $exercise['position_count'] ?> Positions
                                                    </small>
                                                    <small class="text-muted">
                                                        <i class="fas fa-users me-1"></i>
                                                        <?= $exercise['application_count'] ?> Applications
                                                    </small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-clipboard-check me-1"></i>
                                                    <?= ucfirst($exercise['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('reports/dashboard/' . $exercise['id']) ?>"
                                                       class="btn btn-primary btn-sm"
                                                       title="View Reports Dashboard">
                                                        <i class="fas fa-chart-bar me-1"></i>
                                                        Dashboard
                                                    </a>
                                                    <a href="<?= base_url('reports/positions/' . $exercise['id']) ?>"
                                                       class="btn btn-outline-primary btn-sm"
                                                       title="View Positions">
                                                        <i class="fas fa-list me-1"></i>
                                                        Positions
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Available Reports
                    </h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-file-alt text-primary me-2"></i>Application Register</li>
                                <li><i class="fas fa-filter text-primary me-2"></i>Pre-Screening Report</li>
                                <li><i class="fas fa-user-check text-primary me-2"></i>Form 3.7 Profiling Report</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled mb-0">
                                <li><i class="fas fa-user-edit text-primary me-2"></i>Form 3.7A Updated Report</li>
                                <li><i class="fas fa-star text-primary me-2"></i>Application Scoring Report</li>
                                <li><i class="fas fa-comments text-primary me-2"></i>Interview Scoring Report</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
